import { useRef, useEffect, useState } from 'react';
import { Water } from 'three/examples/jsm/objects/Water.js';
import { Sky } from 'three/examples/jsm/objects/Sky.js';
import * as THREE from 'three';
import { useThree, useFrame } from '@react-three/fiber';
import { useTexture } from '@react-three/drei';

export function Ocean() {
  const ref = useRef<THREE.Object3D>();
  const { scene, gl } = useThree();
  const waterNormals = useTexture('textures/waternormals.jpg');
  waterNormals.wrapS = waterNormals.wrapT = THREE.RepeatWrapping;

  const sun = new THREE.Vector3();

  const [water, setWater] = useState<Water>();
  const [sky, setSky] = useState<Sky>();

  useEffect(() => {
    // Crée la géométrie de l'eau
    const waterGeometry = new THREE.PlaneGeometry(10000, 10000);
    const water = new Water(waterGeometry, {
      textureWidth: 1024, // Réduction pour un effet plus doux
      textureHeight: 1024,
      waterNormals: waterNormals,
      sunDirection: new THREE.Vector3(),
      sunColor: 0xffffff,
      waterColor: 0x0a2e3f, // Eau plus bleu/vert pour un rendu réaliste
      distortionScale: 0.15, // Réduction des vagues
      fog: scene.fog !== undefined
    });

    water.rotation.x = -Math.PI / 2;
    water.position.y = -600;
    scene.add(water);
    setWater(water);

    // Crée le ciel
    const sky = new Sky();
    sky.scale.setScalar(10000);
    scene.add(sky);
    setSky(sky);

    // Paramètres du ciel pour un rendu plus naturel
    const skyParams = {
      turbidity: 4, // moins brumeux
      rayleigh: 3,  // ciel un peu plus bleu
      mieCoefficient: 0.003,
      mieDirectionalG: 0.7,
      elevation: 3, // soleil un peu plus haut
      azimuth: 180
    };

    const pmremGenerator = new THREE.PMREMGenerator(gl);
    let renderTarget: THREE.WebGLRenderTarget | null = null;

    const updateSun = () => {
      const phi = THREE.MathUtils.degToRad(90 - skyParams.elevation);
      const theta = THREE.MathUtils.degToRad(skyParams.azimuth);
      sun.setFromSphericalCoords(1, phi, theta);

      sky.material.uniforms.sunPosition.value.copy(sun);
      water.material.uniforms.sunDirection.value.copy(sun).normalize();

      if (renderTarget) renderTarget.dispose();
      const tempScene = new THREE.Scene();
      tempScene.add(sky);
      renderTarget = pmremGenerator.fromScene(tempScene);
      scene.environment = renderTarget.texture;
    };

    updateSun();

    // Nettoyage
    return () => {
      if (renderTarget) renderTarget.dispose();
      if (water) scene.remove(water);
      if (sky) scene.remove(sky);
    };
  }, [gl, scene, waterNormals]);

  useFrame((_state, delta) => {
    if (water) {
      // Animation plus lente pour un effet calme
      water.material.uniforms['time'].value += delta * 0.25;
    }
  });

  return null;
}

export default Ocean;
